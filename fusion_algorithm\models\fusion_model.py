import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from layers.Embed import DataEmbedding_wo_pos
from layers.Transformer_EncDec import Encoder, EncoderLayer
from layers.SelfAttention_Family import FullAttention, AttentionLayer


class PatchEmbedding(nn.Module):
    """Patch嵌入层"""
    def __init__(self, d_model, patch_len, stride, padding, dropout):
        super(PatchEmbedding, self).__init__()
        self.patch_len = patch_len
        self.stride = stride
        self.padding_patch_layer = nn.ReplicationPad1d((0, padding))
        self.value_embedding = nn.Linear(patch_len, d_model, bias=False)
        self.position_embedding = nn.Parameter(torch.randn(1000, d_model))
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        # x: [bs x nvars x seq_len]
        n_vars = x.shape[1]
        x = self.padding_patch_layer(x)
        x = x.unfold(dimension=-1, size=self.patch_len, step=self.stride)
        x = torch.reshape(x, (x.shape[0] * x.shape[1], x.shape[2], x.shape[3]))
        x = self.value_embedding(x)
        return self.dropout(x + self.position_embedding[:x.shape[1], :]), n_vars


class FusionModel(nn.Module):
    """
    融合模型：结合前驱信号检测和异常检测的统一架构
    """
    def __init__(self, configs):
        super(FusionModel, self).__init__()
        self.task_name = configs.task_name
        self.seq_len = configs.seq_len
        self.win_size = getattr(configs, 'win_size', configs.seq_len)
        self.d_model = configs.d_model
        self.patch_len = getattr(configs, 'patch_len', 16)
        self.stride = getattr(configs, 'stride', 8)
        
        # Patch嵌入
        self.patch_embedding = PatchEmbedding(
            configs.d_model, self.patch_len, self.stride, 
            self.stride, configs.dropout
        )
        
        # Transformer编码器
        self.encoder = Encoder(
            [
                EncoderLayer(
                    AttentionLayer(
                        FullAttention(False, configs.factor, attention_dropout=configs.dropout,
                                    output_attention=configs.output_attention),
                        configs.d_model, configs.n_heads),
                    configs.d_model,
                    configs.d_ff,
                    dropout=configs.dropout,
                    activation=configs.activation
                ) for l in range(configs.e_layers)
            ],
            norm_layer=torch.nn.LayerNorm(configs.d_model)
        )
        
        # 异常检测分支
        self.anomaly_head = nn.Linear(configs.d_model, 1)
        
        # 前驱信号检测分支
        self.earlysignal_flatten = nn.Flatten(start_dim=-2)
        self.earlysignal_dropout = nn.Dropout(configs.dropout)
        
        # 动态计算前驱信号分支的输入维度
        patch_num = (self.seq_len - self.patch_len) // self.stride + 2
        earlysignal_input_dim = configs.d_model * patch_num
        
        self.earlysignal_projection = nn.Sequential(
            nn.Linear(earlysignal_input_dim, configs.d_model),
            nn.ReLU(),
            nn.Dropout(configs.dropout),
            nn.Linear(configs.d_model, 2)  # 二分类
        )
        
        # 融合层（用于联合训练）
        self.fusion_weight = nn.Parameter(torch.tensor(0.5))
        
    def forward(self, x_enc, x_mark_enc=None, x_dec=None, x_mark_dec=None, task_mode="anomaly"):
        """
        前向传播
        
        Args:
            x_enc: 输入时间序列 [batch_size, seq_len, features]
            task_mode: 任务模式 ("anomaly", "earlysignal", "fusion")
        """
        batch_size = x_enc.shape[0]
        
        # 标准化
        means = x_enc.mean(1, keepdim=True).detach()
        x_enc = x_enc - means
        stdev = torch.sqrt(torch.var(x_enc, dim=1, keepdim=True, unbiased=False) + 1e-5)
        x_enc /= stdev
        
        # Patch嵌入和编码
        x_enc = x_enc.permute(0, 2, 1)  # [bs, features, seq_len]
        enc_out, n_vars = self.patch_embedding(x_enc)  # [bs*features, patch_num, d_model]
        enc_out, attns = self.encoder(enc_out)
        
        # 重塑为 [bs, features, patch_num, d_model]
        enc_out = torch.reshape(enc_out, (batch_size, n_vars, enc_out.shape[-2], enc_out.shape[-1]))
        
        if task_mode == "anomaly":
            return self._anomaly_detection(enc_out, means, stdev)
        elif task_mode == "earlysignal":
            return self._earlysignal_detection(enc_out)
        elif task_mode == "fusion":
            anomaly_out = self._anomaly_detection(enc_out, means, stdev)
            earlysignal_out = self._earlysignal_detection(enc_out)
            return {
                'anomaly': anomaly_out,
                'earlysignal': earlysignal_out,
                'fusion_weight': torch.sigmoid(self.fusion_weight)
            }
        else:
            raise ValueError(f"Unknown task_mode: {task_mode}")
    
    def _anomaly_detection(self, enc_out, means, stdev):
        """异常检测分支"""
        # [bs, features, patch_num, d_model] -> [bs, features, d_model, patch_num]
        enc_out = enc_out.permute(0, 1, 3, 2)
        
        # 重构头
        dec_out = self.anomaly_head(enc_out.permute(0, 1, 3, 2))  # [bs, features, patch_num, 1]
        dec_out = dec_out.squeeze(-1)  # [bs, features, patch_num]
        
        # 转换回时间序列格式
        dec_out = dec_out.permute(0, 2, 1)  # [bs, patch_num, features]
        
        # 简单的上采样到原始序列长度
        if dec_out.shape[1] != self.seq_len:
            dec_out = F.interpolate(
                dec_out.permute(0, 2, 1), 
                size=self.seq_len, 
                mode='linear', 
                align_corners=False
            ).permute(0, 2, 1)
        
        # 反标准化
        dec_out = dec_out * stdev + means
        
        return dec_out
    
    def _earlysignal_detection(self, enc_out):
        """前驱信号检测分支"""
        # [bs, features, patch_num, d_model] -> [bs, features, d_model, patch_num]
        enc_out = enc_out.permute(0, 1, 3, 2)
        
        # 平坦化
        output = self.earlysignal_flatten(enc_out)  # [bs, features * d_model * patch_num]
        output = self.earlysignal_dropout(output)
        
        # 全局平均池化减少维度
        output = output.view(output.shape[0], -1)
        
        # 分类头
        output = self.earlysignal_projection(output)  # [bs, 2]
        
        return output


class FusionLoss(nn.Module):
    """融合损失函数"""
    def __init__(self, anomaly_weight=0.5, earlysignal_weight=0.5):
        super(FusionLoss, self).__init__()
        self.anomaly_weight = anomaly_weight
        self.earlysignal_weight = earlysignal_weight
        self.mse_loss = nn.MSELoss()
        self.ce_loss = nn.CrossEntropyLoss()
    
    def forward(self, outputs, targets, task_mode="anomaly"):
        """
        计算损失
        
        Args:
            outputs: 模型输出
            targets: 目标值
            task_mode: 任务模式
        """
        if task_mode == "anomaly":
            return self.mse_loss(outputs, targets)
        elif task_mode == "earlysignal":
            return self.ce_loss(outputs, targets.squeeze())
        elif task_mode == "fusion":
            anomaly_loss = self.mse_loss(outputs['anomaly'], targets['anomaly'])
            earlysignal_loss = self.ce_loss(outputs['earlysignal'], targets['earlysignal'].squeeze())
            
            # 动态权重
            fusion_weight = outputs['fusion_weight']
            total_loss = (
                fusion_weight * self.anomaly_weight * anomaly_loss + 
                (1 - fusion_weight) * self.earlysignal_weight * earlysignal_loss
            )
            
            return {
                'total_loss': total_loss,
                'anomaly_loss': anomaly_loss,
                'earlysignal_loss': earlysignal_loss,
                'fusion_weight': fusion_weight
            }
        else:
            raise ValueError(f"Unknown task_mode: {task_mode}")


def create_fusion_model(configs):
    """创建融合模型的工厂函数"""
    return FusionModel(configs)
